#!/bin/bash

# Setup SSM Parameters for Auth Clear Application
# Usage: ./setup-ssm-parameters.sh <stage> <aws-profile>
# Example: ./setup-ssm-parameters.sh prod payrix

set -e

STAGE=${1:-dev}
AWS_PROFILE=${2:-payrix}

if [ "$STAGE" != "prod" ] && [ "$STAGE" != "dev" ]; then
    echo "❌ Error: Stage must be either 'prod' or 'dev'"
    echo "Usage: $0 <stage> <aws-profile>"
    exit 1
fi

echo "🔧 Setting up SSM Parameters for stage: $STAGE with profile: $AWS_PROFILE"

# Parameter prefix for organization
PARAM_PREFIX="/auth-clear/$STAGE"

# Set AWS region
AWS_REGION="us-east-1"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity --profile $AWS_PROFILE --region $AWS_REGION &>/dev/null; then
    echo "❌ Error: AWS CLI not configured for profile '$AWS_PROFILE'"
    echo "Please run: aws configure --profile $AWS_PROFILE"
    exit 1
fi

echo "✅ AWS CLI configured for profile: $AWS_PROFILE"

# Function to create or update a SecureString parameter
create_parameter() {
    local name=$1
    local description=$2
    local read_prompt=$3
    
    echo ""
    echo "📝 Setting up parameter: $name"
    echo "Description: $description"
    
    # Check if parameter already exists
    if aws ssm get-parameter --name "$name" --profile $AWS_PROFILE --region $AWS_REGION &>/dev/null; then
        echo "⚠️  Parameter $name already exists."
        read -p "Do you want to update it? (y/N): " -r
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo -n "$read_prompt"
            read -s value
            echo ""
            
            if [ -z "$value" ]; then
                echo "❌ Empty value provided. Skipping..."
                return
            fi
            
            aws ssm put-parameter \
                --name "$name" \
                --value "$value" \
                --type "SecureString" \
                --description "$description" \
                --overwrite \
                --profile $AWS_PROFILE \
                --region $AWS_REGION
            
            echo "✅ Parameter $name updated successfully"
        else
            echo "⏭️  Skipping parameter $name"
        fi
    else
        echo -n "$read_prompt"
        read -s value
        echo ""
        
        if [ -z "$value" ]; then
            echo "❌ Empty value provided. Skipping..."
            return
        fi
        
        aws ssm put-parameter \
            --name "$name" \
            --value "$value" \
            --type "SecureString" \
            --description "$description" \
            --profile $AWS_PROFILE \
            --region $AWS_REGION
        
        echo "✅ Parameter $name created successfully"
    fi
}

echo ""
echo "🚀 Creating SSM Parameters for $STAGE environment..."

if [ "$STAGE" = "prod" ]; then
    echo ""
    echo "🔐 Production Environment - Setting up secure parameters"
    echo "Note: Values will be stored as SecureString (encrypted)"
    
    create_parameter \
        "$PARAM_PREFIX/payrix/private-api-key" \
        "Payrix Private API Key for $STAGE environment" \
        "Enter Payrix Private API Key for $STAGE: "
    
    create_parameter \
        "$PARAM_PREFIX/payrix/public-api-key" \
        "Payrix Public API Key for $STAGE environment" \
        "Enter Payrix Public API Key for $STAGE: "
    
else
    echo ""
    echo "🔧 Development Environment - Setting up secure parameters"
    echo "Note: Development now uses SSM Parameter Store for consistency and security"
    
    create_parameter \
        "$PARAM_PREFIX/payrix/private-api-key" \
        "Payrix Private API Key for $STAGE environment" \
        "Enter Payrix Private API Key for $STAGE: "
    
    create_parameter \
        "$PARAM_PREFIX/payrix/public-api-key" \
        "Payrix Public API Key for $STAGE environment" \
        "Enter Payrix Public API Key for $STAGE: "
fi

echo ""
echo "📋 Listing created parameters:"
aws ssm get-parameters-by-path \
    --path "$PARAM_PREFIX" \
    --recursive \
    --query "Parameters[*].{Name:Name,Type:Type,LastModifiedDate:LastModifiedDate}" \
    --output table \
    --profile $AWS_PROFILE \
    --region $AWS_REGION

echo ""
echo "✅ SSM Parameter setup complete for $STAGE environment!"
echo ""
echo "🔍 To verify parameters:"
echo "aws ssm get-parameter --name '$PARAM_PREFIX/payrix/private-api-key' --with-decryption --profile $AWS_PROFILE --region $AWS_REGION"
echo ""
echo "🗑️  To delete all parameters:"
echo "aws ssm delete-parameters --names \$(aws ssm get-parameters-by-path --path '$PARAM_PREFIX' --recursive --query 'Parameters[*].Name' --output text --profile $AWS_PROFILE --region $AWS_REGION) --profile $AWS_PROFILE --region $AWS_REGION"
