#!/bin/bash

# Migrate existing .env values to SSM Parameter Store
# Usage: ./migrate-to-ssm.sh <stage> <aws-profile>
# Example: ./migrate-to-ssm.sh prod payrix

set -e

STAGE=${1:-prod}
AWS_PROFILE=${2:-payrix}

if [ "$STAGE" != "prod" ] && [ "$STAGE" != "dev" ]; then
    echo "❌ Error: Stage must be either 'prod' or 'dev'"
    echo "Usage: $0 <stage> <aws-profile>"
    exit 1
fi

echo "🔄 Migrating .env values to SSM Parameter Store for stage: $STAGE"

# Determine which .env file to use
if [ "$STAGE" = "prod" ]; then
    ENV_FILE="../functions/.env.production"
else
    ENV_FILE="../functions/.env"
fi

if [ ! -f "$ENV_FILE" ]; then
    echo "❌ Error: Environment file $ENV_FILE not found"
    exit 1
fi

echo "📖 Reading values from: $ENV_FILE"

# Set AWS region
AWS_REGION="us-east-1"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity --profile $AWS_PROFILE --region $AWS_REGION &>/dev/null; then
    echo "❌ Error: AWS CLI not configured for profile '$AWS_PROFILE'"
    echo "Please run: aws configure --profile $AWS_PROFILE"
    exit 1
fi

# Parameter prefix
PARAM_PREFIX="/auth-clear/$STAGE"

# Function to extract value from .env file
get_env_value() {
    local key=$1
    local file=$2
    grep "^$key=" "$file" | tail -1 | cut -d'=' -f2- | sed 's/^"//' | sed 's/"$//'
}

# Function to create parameter from .env value
migrate_parameter() {
    local env_key=$1
    local param_name=$2
    local description=$3
    
    local value=$(get_env_value "$env_key" "$ENV_FILE")
    
    if [ -z "$value" ] || [ "$value" = "" ]; then
        echo "⚠️  No value found for $env_key in $ENV_FILE, skipping..."
        return
    fi
    
    # Skip commented out values
    if [[ "$value" == "#"* ]]; then
        echo "⚠️  Value for $env_key is commented out, skipping..."
        return
    fi
    
    echo "🔧 Migrating $env_key -> $param_name"
    
    # Check if parameter already exists
    if aws ssm get-parameter --name "$param_name" --profile $AWS_PROFILE --region $AWS_REGION &>/dev/null; then
        echo "⚠️  Parameter $param_name already exists"
        read -p "Do you want to overwrite it? (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "⏭️  Skipping $param_name"
            return
        fi
    fi
    
    aws ssm put-parameter \
        --name "$param_name" \
        --value "$value" \
        --type "SecureString" \
        --description "$description" \
        --overwrite \
        --profile $AWS_PROFILE \
        --region $AWS_REGION
    
    echo "✅ Successfully migrated $env_key"
}

echo ""
echo "🚀 Starting migration..."

# Migrate sensitive keys to SSM Parameter Store for secure storage
migrate_parameter "PAYRIX_PRIVATE_API_KEY" "$PARAM_PREFIX/payrix/private-api-key" "Payrix Private API Key for $STAGE environment"
migrate_parameter "PAYRIX_PUBLIC_API_KEY" "$PARAM_PREFIX/payrix/public-api-key" "Payrix Public API Key for $STAGE environment"

echo ""
echo "📋 Verifying migrated parameters:"
aws ssm get-parameters-by-path \
    --path "$PARAM_PREFIX" \
    --recursive \
    --query "Parameters[*].{Name:Name,Type:Type,LastModifiedDate:LastModifiedDate}" \
    --output table \
    --profile $AWS_PROFILE \
    --region $AWS_REGION

echo ""
echo "✅ Migration complete!"
echo ""
echo "🔒 Your sensitive keys are now securely stored in SSM Parameter Store"
echo "📝 The application will now use SSM parameters for all environments"
echo ""
echo "Next steps:"
echo "1. Deploy your functions: npm run deploy:functions:$STAGE"
echo "2. Test that the application works with SSM parameters"
echo "3. Optional: Remove sensitive keys from .env files (they're no longer used)"
echo "4. Repeat this process for other environments if needed"
