#!/bin/bash

# Setup SSM Parameters for both Dev and Prod environments
# Usage: ./setup-all-ssm-parameters.sh <aws-profile>
# Example: ./setup-all-ssm-parameters.sh payrix

set -e

AWS_PROFILE=${1:-payrix}

echo "🚀 Setting up SSM Parameters for ALL environments (dev and prod)"
echo "AWS Profile: $AWS_PROFILE"

# Set AWS region
AWS_REGION="us-east-1"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity --profile $AWS_PROFILE --region $AWS_REGION &>/dev/null; then
    echo "❌ Error: AWS CLI not configured for profile '$AWS_PROFILE'"
    echo "Please run: aws configure --profile $AWS_PROFILE"
    exit 1
fi

echo "✅ AWS CLI configured for profile: $AWS_PROFILE"
echo ""

# Setup development environment
echo "🔧 Setting up DEVELOPMENT environment parameters..."
echo "═══════════════════════════════════════════════════════"
./scripts/setup-ssm-parameters.sh dev $AWS_PROFILE

echo ""
echo ""

# Setup production environment
echo "🔐 Setting up PRODUCTION environment parameters..."
echo "═══════════════════════════════════════════════════════"
./scripts/setup-ssm-parameters.sh prod $AWS_PROFILE

echo ""
echo "🎉 All environments configured!"
echo ""
echo "📋 Summary of all parameters:"
echo "═══════════════════════════════════════════════════════"

echo ""
echo "🔧 Development Parameters:"
aws ssm get-parameters-by-path \
    --path "/auth-clear/dev" \
    --recursive \
    --query "Parameters[*].{Name:Name,Type:Type,LastModifiedDate:LastModifiedDate}" \
    --output table \
    --profile $AWS_PROFILE \
    --region $AWS_REGION 2>/dev/null || echo "No dev parameters found"

echo ""
echo "🔐 Production Parameters:"
aws ssm get-parameters-by-path \
    --path "/auth-clear/prod" \
    --recursive \
    --query "Parameters[*].{Name:Name,Type:Type,LastModifiedDate:LastModifiedDate}" \
    --output table \
    --profile $AWS_PROFILE \
    --region $AWS_REGION 2>/dev/null || echo "No prod parameters found"

echo ""
echo "✅ Setup complete for both environments!"
echo ""
echo "🚀 Next Steps:"
echo "1. Deploy dev functions:  npm run deploy:functions:dev"
echo "2. Deploy prod functions: npm run deploy:functions:prod"
echo "3. Test both environments work with SSM parameters"
echo "4. Optional: Remove sensitive keys from .env files"
