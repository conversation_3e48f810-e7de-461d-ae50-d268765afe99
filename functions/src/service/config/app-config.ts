import { parameterService } from "../ssm/parameter-service.js";

export interface PayrixConfig {
  apiUrl: string;
  privateApiKey: string;
  publicApiKey: string;
}

export interface AppConfig {
  stage: string;
  payrix: PayrixConfig;
  frontendUrl: string;
  tables: {
    paymentTokens: string;
    merchantData: string;
  };
}

export class ConfigService {
  private static instance: ConfigService;
  private config: AppConfig | null = null;

  private constructor() {}

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  async getConfig(): Promise<AppConfig> {
    if (this.config) {
      return this.config;
    }

    const stage = process.env.STAGE || "dev";
    const isOffline = process.env.IS_OFFLINE === "true";
    const parameterPrefix = `/auth-clear/${stage}`;

    let payrixPrivateKey: string;
    let payrixPublicKey: string;

    // For local development with serverless offline, use environment variables
    if (isOffline && process.env.PAYRIX_PRIVATE_API_KEY && process.env.PAYRIX_PUBLIC_API_KEY) {
      console.log("Running in offline mode, using environment variables");
      payrixPrivateKey = process.env.PAYRIX_PRIVATE_API_KEY;
      payrixPublicKey = process.env.PAYRIX_PUBLIC_API_KEY;
    } else {
      // Use SSM Parameter Store for deployed environments
      try {
        const parameters = await parameterService.getParameters([
          `${parameterPrefix}/payrix/private-api-key`,
          `${parameterPrefix}/payrix/public-api-key`,
        ]);

        const privateKeyParam = parameters.find((p) => p.name.includes("private-api-key"));
        const publicKeyParam = parameters.find((p) => p.name.includes("public-api-key"));

        if (!privateKeyParam || !publicKeyParam) {
          throw new Error(`Missing required Payrix API keys in SSM Parameter Store for stage: ${stage}`);
        }

        payrixPrivateKey = privateKeyParam.value;
        payrixPublicKey = publicKeyParam.value;
      } catch (error) {
        console.error(`Failed to load configuration from SSM for stage ${stage}:`, error);

        // Fallback to environment variables if SSM fails
        if (process.env.PAYRIX_PRIVATE_API_KEY && process.env.PAYRIX_PUBLIC_API_KEY) {
          console.warn("SSM failed, falling back to environment variables");
          payrixPrivateKey = process.env.PAYRIX_PRIVATE_API_KEY;
          payrixPublicKey = process.env.PAYRIX_PUBLIC_API_KEY;
        } else {
          throw new Error(`Configuration loading failed for ${stage}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    }

    this.config = {
      stage,
      payrix: {
        apiUrl: process.env.PAYRIX_API_URL || "",
        privateApiKey: payrixPrivateKey,
        publicApiKey: payrixPublicKey,
      },
      frontendUrl: process.env.FRONTEND_URL || "",
      tables: {
        paymentTokens: process.env.PAYMENT_TOKENS_TABLE_NAME || "",
        merchantData: process.env.MERCHANT_DATA_TABLE_NAME || "",
      },
    };

    return this.config;
  }

  clearCache(): void {
    this.config = null;
    parameterService.clearCache();
  }
}

export const configService = ConfigService.getInstance();
