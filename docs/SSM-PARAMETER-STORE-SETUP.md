# AWS Systems Manager Parameter Store Setup

This document explains how to migrate your Auth Clear application from using `.env` files to AWS Systems Manager Parameter Store for secure credential management across all environments.

## Overview

The application now uses AWS Systems Manager Parameter Store for managing sensitive credentials in both development and production environments. This provides:

- ✅ **Enhanced Security**: Encrypted storage of sensitive keys
- ✅ **Centralized Management**: All secrets managed through AWS
- ✅ **Audit Trail**: AWS CloudTrail tracks parameter access
- ✅ **Fine-grained Access Control**: IAM-based permissions
- ✅ **Consistent Environment Management**: Same approach for dev and prod

## Parameter Structure

Parameters are organized with the following naming convention:
```
/auth-clear/{stage}/payrix/private-api-key
/auth-clear/{stage}/payrix/public-api-key
```

Where `{stage}` is either `dev` or `prod`.

## Prerequisites

1. **AWS CLI configured** with appropriate profile:
   ```bash
   aws configure --profile payrix
   ```

2. **IAM Permissions** - Your AWS user/role needs:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "ssm:GetParameter",
           "ssm:GetParameters",
           "ssm:PutParameter"
         ],
         "Resource": "arn:aws:ssm:us-east-1:*:parameter/auth-clear/*"
       }
     ]
   }
   ```

## Setup Methods

### Method 1: Automated Migration (Recommended)

Use the migration script to automatically transfer values from your existing `.env` files:

```bash
# For production
./scripts/migrate-to-ssm.sh prod payrix

# For development
./scripts/migrate-to-ssm.sh dev payrix
```

### Method 2: Manual Setup

Use the interactive setup script:

```bash
# For production
./scripts/setup-ssm-parameters.sh prod payrix

# For development
./scripts/setup-ssm-parameters.sh dev payrix
```

### Method 3: AWS CLI Direct

Create parameters directly using AWS CLI:

```bash
# Production parameters
aws ssm put-parameter \
  --name "/auth-clear/prod/payrix/private-api-key" \
  --value "your-private-key" \
  --type "SecureString" \
  --description "Payrix Private API Key for prod environment" \
  --profile payrix

aws ssm put-parameter \
  --name "/auth-clear/prod/payrix/public-api-key" \
  --value "your-public-key" \
  --type "SecureString" \
  --description "Payrix Public API Key for prod environment" \
  --profile payrix

# Development parameters
aws ssm put-parameter \
  --name "/auth-clear/dev/payrix/private-api-key" \
  --value "your-dev-private-key" \
  --type "SecureString" \
  --description "Payrix Private API Key for dev environment" \
  --profile payrix

aws ssm put-parameter \
  --name "/auth-clear/dev/payrix/public-api-key" \
  --value "your-dev-public-key" \
  --type "SecureString" \
  --description "Payrix Public API Key for dev environment" \
  --profile payrix
```

## Verification

### Check Parameters Exist
```bash
# List all parameters for a stage
aws ssm get-parameters-by-path \
  --path "/auth-clear/prod" \
  --recursive \
  --query "Parameters[*].{Name:Name,Type:Type}" \
  --output table \
  --profile payrix

# Get a specific parameter (with decryption)
aws ssm get-parameter \
  --name "/auth-clear/prod/payrix/private-api-key" \
  --with-decryption \
  --query "Parameter.Value" \
  --output text \
  --profile payrix
```

### Test Application Configuration
```bash
# Test configuration loading locally
cd functions
npm run offline -- --stage prod

# Check logs for successful parameter loading
```

## Environment Variables Still Used

The following non-sensitive environment variables are still sourced from `.env` files or serverless.yml:

- `STAGE` - Environment stage (dev/prod)
- `PAYRIX_API_URL` - API endpoint URL
- `FRONTEND_URL` - Frontend application URL
- `PAYMENT_TOKENS_TABLE_NAME` - DynamoDB table name
- `MERCHANT_DATA_TABLE_NAME` - DynamoDB table name

## Deployment Process

### 1. Set Up Parameters
First, ensure parameters are configured for your target stage:
```bash
./scripts/setup-ssm-parameters.sh prod payrix
```

### 2. Deploy Functions
Deploy your Lambda functions as usual:
```bash
npm run deploy:functions:prod
```

The application will automatically load parameters from SSM during runtime.

## Troubleshooting

### Common Issues

1. **"Parameter not found" errors**
   - Verify parameters exist: `aws ssm get-parameters-by-path --path "/auth-clear/prod" --profile payrix`
   - Check parameter names match exactly

2. **"Access denied" errors**
   - Verify IAM permissions for SSM Parameter Store
   - Check that your execution role has the correct policies

3. **"Configuration loading failed" errors**
   - Check CloudWatch logs for detailed error messages
   - Verify AWS region matches between Lambda and parameters

4. **Slow cold start times**
   - Parameters are cached for 5 minutes to improve performance
   - Cold starts will have initial latency for parameter retrieval

### Debug Commands

```bash
# Check if parameters exist
aws ssm describe-parameters \
  --parameter-filters "Key=Name,Values=/auth-clear/" \
  --profile payrix

# Test parameter retrieval
aws ssm get-parameters \
  --names "/auth-clear/prod/payrix/private-api-key" "/auth-clear/prod/payrix/public-api-key" \
  --with-decryption \
  --profile payrix

# View CloudWatch logs
aws logs tail /aws/lambda/auth-clear-functions-prod-onboardMerchant --follow --profile payrix
```

## Security Best Practices

1. **Use SecureString Type**: All sensitive parameters should use `SecureString` type for encryption
2. **Least Privilege Access**: Grant minimal required permissions to access parameters
3. **Regular Key Rotation**: Periodically rotate API keys and update parameters
4. **Monitor Access**: Use CloudTrail to audit parameter access
5. **Environment Separation**: Keep dev and prod parameters completely separate

## Cleanup

To remove all parameters for a stage:
```bash
# Get all parameter names for deletion
PARAMS=$(aws ssm get-parameters-by-path \
  --path "/auth-clear/prod" \
  --recursive \
  --query "Parameters[*].Name" \
  --output text \
  --profile payrix)

# Delete all parameters
aws ssm delete-parameters --names $PARAMS --profile payrix
```

## Migration Checklist

- [ ] Set up SSM parameters for dev environment
- [ ] Set up SSM parameters for prod environment
- [ ] Test application in dev with SSM parameters
- [ ] Deploy and test in production
- [ ] Remove sensitive keys from `.env` files
- [ ] Update team documentation
- [ ] Train team on new parameter management process

## Support

For questions or issues:
1. Check CloudWatch logs for detailed error messages
2. Verify IAM permissions and parameter existence
3. Test with AWS CLI commands above
4. Review this documentation for troubleshooting steps
