# SSM Parameter Store - Local Development Guide

## Overview

The application uses AWS Systems Manager Parameter Store for secure credential management in all environments. However, for local development with `serverless offline`, there's a fallback mechanism to use environment variables.

## How It Works

### Production/Deployed Environments
- **Primary Source**: AWS SSM Parameter Store
- **Parameters**: `/auth-clear/{stage}/payrix/private-api-key` and `/auth-clear/{stage}/payrix/public-api-key`
- **Fallback**: Environment variables (if SSM fails)

### Local Development (serverless offline)
- **Primary Source**: Environment variables from `.env` file
- **Detection**: When `IS_OFFLINE=true` (automatically set by serverless offline)
- **No AWS credentials required**

## Configuration Priority

1. **If running offline** (`IS_OFFLINE=true`):
   - Uses environment variables from `.env` file
   
2. **If deployed** (Lambda/AWS):
   - First tries SSM Parameter Store
   - Falls back to environment variables if SSM fails

## Local Development Setup

1. **Ensure you have a `.env` file** in the `functions` directory:
   ```bash
   cd functions
   # Copy from example if needed
   cp .env.example .env
   ```

2. **Your `.env` file should contain**:
   ```env
   STAGE=dev
   PAYRIX_API_URL=https://test-api.payrix.com
   PAYRIX_PRIVATE_API_KEY=your-dev-private-key
   PAYRIX_PUBLIC_API_KEY=your-dev-public-key
   PAYMENT_TOKENS_TABLE_NAME=PaymentTokens-dev
   MERCHANT_DATA_TABLE_NAME=MerchantData-dev
   FRONTEND_URL=http://localhost:5173
   ```

3. **Run serverless offline**:
   ```bash
   npm run offline
   ```

## Production Setup

For production deployments, set up SSM parameters:

```bash
# Use the migration script if you have .env.production
npm run migrate-ssm:prod

# Or use the interactive setup
npm run setup-ssm:prod
```

## Debugging

### Check Configuration Loading
The app will log which method it's using:
- `"Running in offline mode, using environment variables"` - Using local .env
- `"SSM failed, falling back to environment variables"` - SSM failed, using env vars
- No message - Successfully loaded from SSM

### Common Issues

1. **"Could not load credentials from any providers"**
   - This happens in serverless offline without AWS credentials
   - The app will automatically fall back to environment variables

2. **"Missing required Payrix API keys"**
   - Ensure your `.env` file has both API keys defined
   - For deployed environments, ensure SSM parameters exist

3. **"Configuration loading failed"**
   - Check CloudWatch logs for deployed functions
   - Verify IAM permissions for SSM access
   - Ensure parameters exist in the correct path

## Best Practices

1. **Never commit `.env` files** with real credentials
2. **Use different API keys** for dev vs prod
3. **Rotate keys regularly** and update in SSM
4. **Monitor access** via CloudTrail for production
