{"name": "auth-clear", "version": "1.0.0", "description": "auth-clear", "main": "handler.js", "type": "module", "scripts": {"generate-env": "./scripts/generate-env.sh dev payrix", "generate-env:prod": "./scripts/generate-env.sh prod payrix", "setup-ssm:dev": "./scripts/setup-ssm-parameters.sh dev payrix", "setup-ssm:prod": "./scripts/setup-ssm-parameters.sh prod payrix", "migrate-ssm:dev": "./scripts/migrate-to-ssm.sh dev payrix", "migrate-ssm:prod": "./scripts/migrate-to-ssm.sh prod payrix", "lint": "eslint . --ext .ts", "start:frontend": "cd frontend && npm run dev", "offline": "cd functions && serverless offline start --stage dev --aws-profile payrix", "offline:prod": "cd functions && serverless offline start --stage prod --aws-profile payrix", "deploy:infra:dev": "cd infra && serverless deploy --stage dev --aws-profile payrix", "deploy:infra:prod": "cd infra && serverless deploy --stage prod --aws-profile payrix", "deploy:functions:dev": "cd functions && serverless deploy --stage dev --aws-profile payrix", "deploy:functions:prod": "cd functions && serverless deploy --stage prod --aws-profile payrix", "deploy:frontend:infra:dev": "cd frontend && serverless deploy --stage dev --aws-profile payrix", "deploy:frontend:infra:prod": "cd frontend && serverless deploy --stage prod --aws-profile payrix", "deploy:frontend:files:dev": "cd frontend && npm run deploy:dev", "deploy:frontend:files:prod": "cd frontend && npm run deploy:prod", "deploy:frontend:dev": "npm run deploy:frontend:infra:dev && npm run deploy:frontend:files:dev", "deploy:frontend:prod": "npm run deploy:frontend:infra:prod && npm run deploy:frontend:files:prod", "deploy:dev": "npm run deploy:infra:dev && npm run deploy:frontend:dev && npm run deploy:functions:dev", "deploy:prod": "npm run deploy:infra:prod && npm run deploy:frontend:prod && npm run deploy:functions:prod"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-ssm": "^3.536.0", "@types/aws-lambda": "^8.10.136", "@types/node": "^22.14.0", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1692.0", "serverless-export-env": "^2.2.0", "serverless-iam-roles-per-function": "^3.2.0", "serverless-offline": "^14.4.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "devDependencies": {"@eslint/js": "^9.28.0", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "esbuild": "^0.25.5", "eslint": "^9.28.0", "globals": "^16.2.0", "serverless-esbuild": "^1.55.0", "typescript-eslint": "^8.33.1"}}